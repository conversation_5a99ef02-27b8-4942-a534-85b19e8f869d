<%- include('../../partials/layout', {
  title: 'Create Campaign',
  currentPath: '/admin/campaigns/create',
  body: `
    <div class="container mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Create New Campaign</h1>
        <div>
          <button id="backToCampaignsBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Campaigns
          </button>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Campaign Form -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-xl font-semibold mb-4">Campaign Details</h2>
          
          <form id="campaignForm">
            <div class="mb-4">
              <label for="campaignName" class="block text-sm font-medium text-gray-700 mb-2">Campaign Name</label>
              <input
                type="text"
                id="campaignName"
                name="name"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Enter campaign name"
              />
            </div>

            <div class="mb-4">
              <label for="emailSubject" class="block text-sm font-medium text-gray-700 mb-2">Email Subject</label>
              <input
                type="text"
                id="emailSubject"
                name="subject"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Enter email subject"
              />
            </div>

            <div class="mb-4">
              <label for="targetAudience" class="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
              <select
                id="targetAudience"
                name="target_audience"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="all">All Users</option>
                <option value="active">Active Users Only</option>
                <option value="inactive">Inactive Users Only</option>
                <option value="trial">Trial Users Only</option>
                <option value="exclude_trial">Exclude Trial Users</option>
                <option value="exclude_active">Exclude Active Users</option>
                <option value="exclude_inactive">Exclude Inactive Users</option>
              </select>
            </div>

            <div class="mb-4">
              <label for="scheduledAt" class="block text-sm font-medium text-gray-700 mb-2">Schedule Send (Optional)</label>
              <input
                type="datetime-local"
                id="scheduledAt"
                name="scheduled_at"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <p class="text-sm text-gray-500 mt-1">Leave empty to save as draft</p>
            </div>

            <div class="mb-6">
              <label for="htmlTemplate" class="block text-sm font-medium text-gray-700 mb-2">Email Template (HTML)</label>
              <div class="border border-gray-300 rounded-md">
                <div class="bg-gray-50 px-3 py-2 border-b border-gray-300">
                  <div class="flex space-x-2">
                    <button type="button" onclick="insertVariable('{{user_email}}')" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">User Email</button>
                    <button type="button" onclick="insertVariable('{{unsubscribe_url}}')" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Unsubscribe</button>
                    <button type="button" onclick="loadTemplate('welcome')" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Welcome Template</button>
                    <button type="button" onclick="loadTemplate('newsletter')" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Newsletter Template</button>
                  </div>
                </div>
                <textarea
                  id="htmlTemplate"
                  name="html_template"
                  required
                  rows="15"
                  class="w-full px-3 py-2 border-0 focus:outline-none focus:ring-0 resize-none"
                  placeholder="Enter your HTML email template here..."
                ></textarea>
              </div>
            </div>

            <div class="flex space-x-4">
              <button
                type="submit"
                class="flex-1 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Save Campaign
              </button>
              <button
                type="button"
                id="previewBtn"
                class="flex-1 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
              >
                Preview Email
              </button>
            </div>
          </form>
        </div>

        <!-- Email Preview -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-xl font-semibold mb-4">Email Preview</h2>
          
          <div class="border border-gray-300 rounded-md">
            <div class="bg-gray-50 px-4 py-2 border-b border-gray-300">
              <div class="text-sm">
                <strong>From:</strong> Kitsify &lt;<EMAIL>&gt;<br>
                <strong>To:</strong> <EMAIL><br>
                <strong>Subject:</strong> <span id="previewSubject">Your email subject will appear here</span>
              </div>
            </div>
            <div id="emailPreview" class="p-4 min-h-96 bg-white">
              <p class="text-gray-500 italic">Email preview will appear here when you click "Preview Email"</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <script>
      // Email templates
      const templates = {
        welcome: \`<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to Kitsify</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
    .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
    .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Welcome to Kitsify!</h1>
    </div>
    <div class="content">
      <h2>Hello there!</h2>
      <p>Welcome to Kitsify! We're excited to have you on board.</p>
      <p>Your email: {{user_email}}</p>
      <a href="https://kitsify.com/tools" class="button">Get Started</a>
      <p>If you have any questions, feel free to reach out to our support team.</p>
    </div>
    <div class="footer">
      <p>This email was sent to {{user_email}}</p>
      <p><a href="{{unsubscribe_url}}">Unsubscribe</a> | © 2024 Kitsify. All rights reserved.</p>
    </div>
  </div>
</body>
</html>\`,
        newsletter: \`<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Kitsify Newsletter</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
    .content { background: white; padding: 30px; }
    .section { margin-bottom: 30px; }
    .button { display: inline-block; background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; }
    .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Kitsify Newsletter</h1>
    </div>
    <div class="content">
      <div class="section">
        <h2>What's New This Week</h2>
        <p>Here are the latest updates and features we've added to Kitsify...</p>
      </div>
      <div class="section">
        <h2>Featured Tools</h2>
        <p>Check out these amazing tools that can boost your productivity...</p>
        <a href="https://kitsify.com/tools" class="button">Explore Tools</a>
      </div>
    </div>
    <div class="footer">
      <p>This email was sent to {{user_email}}</p>
      <p><a href="{{unsubscribe_url}}">Unsubscribe</a> | © 2024 Kitsify. All rights reserved.</p>
    </div>
  </div>
</body>
</html>\`
      };

      function insertVariable(variable) {
        const textarea = document.getElementById('htmlTemplate');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const text = textarea.value;
        
        textarea.value = text.substring(0, start) + variable + text.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start + variable.length, start + variable.length);
        
        updatePreview();
      }

      function loadTemplate(templateName) {
        if (templates[templateName]) {
          document.getElementById('htmlTemplate').value = templates[templateName];
          updatePreview();
        }
      }

      function updatePreview() {
        const subject = document.getElementById('emailSubject').value || 'Your email subject will appear here';
        const htmlContent = document.getElementById('htmlTemplate').value;
        
        document.getElementById('previewSubject').textContent = subject;
        
        if (htmlContent.trim()) {
          // Replace variables with sample data for preview
          let previewContent = htmlContent
            .replace(/{{user_email}}/g, '<EMAIL>')
            .replace(/{{unsubscribe_url}}/g, 'https://kitsify.com/unsubscribe')
            .replace(/{{tracking_url}}/g, 'https://kitsify.com/track');
          
          document.getElementById('emailPreview').innerHTML = previewContent;
        } else {
          document.getElementById('emailPreview').innerHTML = '<p class="text-gray-500 italic">Email preview will appear here when you add content</p>';
        }
      }

      // Form submission
      document.getElementById('campaignForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const campaignData = {
          name: formData.get('name'),
          subject: formData.get('subject'),
          html_template: formData.get('html_template'),
          target_audience: formData.get('target_audience'),
        };

        if (formData.get('scheduled_at')) {
          campaignData.scheduled_at = formData.get('scheduled_at');
        }

        try {
          const response = await axios.post('/campaigns', campaignData);
          showToast('Campaign created successfully');
          window.location.href = '/admin/campaigns';
        } catch (error) {
          handleApiError(error);
        }
      });

      // Preview button
      document.getElementById('previewBtn').addEventListener('click', updatePreview);

      // Auto-update preview when subject changes
      document.getElementById('emailSubject').addEventListener('input', updatePreview);
      document.getElementById('htmlTemplate').addEventListener('input', updatePreview);

      // Back button
      document.getElementById('backToCampaignsBtn').addEventListener('click', () => {
        window.location.href = '/admin/campaigns';
      });

      // Initialize
      document.addEventListener('DOMContentLoaded', () => {
        updatePreview();
      });
    </script>
  `
}) %>
